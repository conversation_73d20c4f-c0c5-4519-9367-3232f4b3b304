// OpenCV.js-based chessboard detection
export class BoardDetector {
    constructor() {
        this.cv = null;
        this.initialized = false;
    }
    
    async initialize() {
        if (this.initialized) return true;
        
        try {
            // Wait for OpenCV.js to load
            if (typeof window !== 'undefined' && window.cv) {
                this.cv = window.cv;
                
                // Wait for OpenCV to be ready
                if (this.cv.getBuildInformation) {
                    console.log('OpenCV.js version:', this.cv.getBuildInformation());
                    this.initialized = true;
                    return true;
                } else {
                    // OpenCV is still loading
                    return new Promise((resolve) => {
                        this.cv.onRuntimeInitialized = () => {
                            console.log('OpenCV.js initialized');
                            this.initialized = true;
                            resolve(true);
                        };
                    });
                }
            } else {
                console.warn('OpenCV.js not available, using fallback detection');
                return false;
            }
        } catch (error) {
            console.error('Failed to initialize OpenCV.js:', error);
            return false;
        }
    }
    
    async detectBoardCorners(canvas) {
        if (!await this.initialize()) {
            return this.getFallbackHomography();
        }
        
        try {
            // Convert canvas to OpenCV Mat
            const src = this.cv.imread(canvas);
            
            // Try multiple detection methods
            let corners = await this.detectCornersWithLines(src);
            
            if (!corners || corners.length !== 4) {
                corners = await this.detectCornersWithChessboard(src);
            }
            
            if (!corners || corners.length !== 4) {
                corners = await this.detectCornersWithContours(src);
            }
            
            src.delete();
            
            if (corners && corners.length === 4) {
                return this.computeHomography(corners, 800); // 800x800 canonical size
            } else {
                console.warn('Corner detection failed, using fallback');
                return this.getFallbackHomography();
            }
            
        } catch (error) {
            console.error('Board detection error:', error);
            return this.getFallbackHomography();
        }
    }
    
    async detectCornersWithLines(src) {
        try {
            const gray = new this.cv.Mat();
            this.cv.cvtColor(src, gray, this.cv.COLOR_RGBA2GRAY);
            
            // Apply Gaussian blur to reduce noise
            const blurred = new this.cv.Mat();
            this.cv.GaussianBlur(gray, blurred, new this.cv.Size(5, 5), 0);
            
            // Edge detection
            const edges = new this.cv.Mat();
            this.cv.Canny(blurred, edges, 50, 150, 3);
            
            // Detect lines using Hough transform
            const lines = new this.cv.Mat();
            this.cv.HoughLinesP(edges, lines, 1, Math.PI / 180, 100, 100, 50);
            
            // Separate lines into horizontal and vertical
            const horizontalLines = [];
            const verticalLines = [];
            
            for (let i = 0; i < lines.rows; i++) {
                const line = lines.data32S.slice(i * 4, (i + 1) * 4);
                const [x1, y1, x2, y2] = line;
                
                const dx = x2 - x1;
                const dy = y2 - y1;
                const angle = Math.atan2(dy, dx);
                
                // Classify as horizontal or vertical
                if (Math.abs(angle) < Math.PI / 6 || Math.abs(Math.abs(angle) - Math.PI) < Math.PI / 6) {
                    horizontalLines.push({ x1, y1, x2, y2, angle });
                } else if (Math.abs(Math.abs(angle) - Math.PI / 2) < Math.PI / 6) {
                    verticalLines.push({ x1, y1, x2, y2, angle });
                }
            }
            
            // Clean up
            gray.delete();
            blurred.delete();
            edges.delete();
            lines.delete();
            
            // Find extreme lines
            if (horizontalLines.length >= 2 && verticalLines.length >= 2) {
                const corners = this.findCornersFromLines(horizontalLines, verticalLines, src.cols, src.rows);
                return corners;
            }
            
            return null;
            
        } catch (error) {
            console.error('Line detection error:', error);
            return null;
        }
    }
    
    findCornersFromLines(horizontalLines, verticalLines, width, height) {
        // Sort horizontal lines by y-coordinate of midpoint
        horizontalLines.sort((a, b) => {
            const midA = (a.y1 + a.y2) / 2;
            const midB = (b.y1 + b.y2) / 2;
            return midA - midB;
        });
        
        // Sort vertical lines by x-coordinate of midpoint
        verticalLines.sort((a, b) => {
            const midA = (a.x1 + a.x2) / 2;
            const midB = (b.x1 + b.x2) / 2;
            return midA - midB;
        });
        
        // Take extreme lines
        const topLine = horizontalLines[0];
        const bottomLine = horizontalLines[horizontalLines.length - 1];
        const leftLine = verticalLines[0];
        const rightLine = verticalLines[verticalLines.length - 1];
        
        // Calculate intersections
        const corners = [
            this.lineIntersection(topLine, leftLine),     // Top-left
            this.lineIntersection(topLine, rightLine),    // Top-right
            this.lineIntersection(bottomLine, rightLine), // Bottom-right
            this.lineIntersection(bottomLine, leftLine)   // Bottom-left
        ];
        
        // Validate corners are within image bounds
        const validCorners = corners.filter(corner => 
            corner && corner.x >= 0 && corner.x < width && 
            corner.y >= 0 && corner.y < height
        );
        
        return validCorners.length === 4 ? validCorners : null;
    }
    
    lineIntersection(line1, line2) {
        const { x1: x1a, y1: y1a, x2: x2a, y2: y2a } = line1;
        const { x1: x1b, y1: y1b, x2: x2b, y2: y2b } = line2;
        
        const denom = (x1a - x2a) * (y1b - y2b) - (y1a - y2a) * (x1b - x2b);
        
        if (Math.abs(denom) < 1e-10) return null; // Lines are parallel
        
        const t = ((x1a - x1b) * (y1b - y2b) - (y1a - y1b) * (x1b - x2b)) / denom;
        
        return {
            x: x1a + t * (x2a - x1a),
            y: y1a + t * (y2a - y1a)
        };
    }
    
    async detectCornersWithChessboard(src) {
        try {
            const gray = new this.cv.Mat();
            this.cv.cvtColor(src, gray, this.cv.COLOR_RGBA2GRAY);
            
            // Try different chessboard sizes
            const boardSizes = [
                new this.cv.Size(7, 7),   // 8x8 board has 7x7 internal corners
                new this.cv.Size(6, 6),
                new this.cv.Size(5, 5)
            ];
            
            for (const boardSize of boardSizes) {
                const corners = new this.cv.Mat();
                const found = this.cv.findChessboardCorners(gray, boardSize, corners);
                
                if (found) {
                    // Extract corner coordinates
                    const cornerPoints = [];
                    for (let i = 0; i < corners.rows; i++) {
                        const x = corners.data32F[i * 2];
                        const y = corners.data32F[i * 2 + 1];
                        cornerPoints.push({ x, y });
                    }
                    
                    corners.delete();
                    gray.delete();
                    
                    // Convert internal corners to board corners
                    return this.extrapolateBoardCorners(cornerPoints, boardSize);
                }
                
                corners.delete();
            }
            
            gray.delete();
            return null;
            
        } catch (error) {
            console.error('Chessboard detection error:', error);
            return null;
        }
    }
    
    extrapolateBoardCorners(internalCorners, boardSize) {
        if (internalCorners.length === 0) return null;
        
        // For simplicity, use the bounding box of internal corners
        // and expand it slightly to get board corners
        let minX = Infinity, minY = Infinity;
        let maxX = -Infinity, maxY = -Infinity;
        
        internalCorners.forEach(corner => {
            minX = Math.min(minX, corner.x);
            minY = Math.min(minY, corner.y);
            maxX = Math.max(maxX, corner.x);
            maxY = Math.max(maxY, corner.y);
        });
        
        // Expand by estimated square size
        const squareWidth = (maxX - minX) / (boardSize.width - 1);
        const squareHeight = (maxY - minY) / (boardSize.height - 1);
        
        return [
            { x: minX - squareWidth, y: minY - squareHeight },     // Top-left
            { x: maxX + squareWidth, y: minY - squareHeight },     // Top-right
            { x: maxX + squareWidth, y: maxY + squareHeight },     // Bottom-right
            { x: minX - squareWidth, y: maxY + squareHeight }      // Bottom-left
        ];
    }
    
    async detectCornersWithContours(src) {
        try {
            const gray = new this.cv.Mat();
            this.cv.cvtColor(src, gray, this.cv.COLOR_RGBA2GRAY);
            
            // Apply threshold
            const binary = new this.cv.Mat();
            this.cv.threshold(gray, binary, 0, 255, this.cv.THRESH_BINARY + this.cv.THRESH_OTSU);
            
            // Find contours
            const contours = new this.cv.MatVector();
            const hierarchy = new this.cv.Mat();
            this.cv.findContours(binary, contours, hierarchy, this.cv.RETR_EXTERNAL, this.cv.CHAIN_APPROX_SIMPLE);
            
            // Find largest rectangular contour
            let bestContour = null;
            let maxArea = 0;
            
            for (let i = 0; i < contours.size(); i++) {
                const contour = contours.get(i);
                const area = this.cv.contourArea(contour);
                
                if (area > maxArea && area > src.cols * src.rows * 0.1) {
                    // Approximate contour to polygon
                    const approx = new this.cv.Mat();
                    const epsilon = 0.02 * this.cv.arcLength(contour, true);
                    this.cv.approxPolyDP(contour, approx, epsilon, true);
                    
                    if (approx.rows === 4) {
                        bestContour = approx;
                        maxArea = area;
                    } else {
                        approx.delete();
                    }
                }
            }
            
            // Clean up
            gray.delete();
            binary.delete();
            contours.delete();
            hierarchy.delete();
            
            if (bestContour) {
                const corners = [];
                for (let i = 0; i < 4; i++) {
                    corners.push({
                        x: bestContour.data32S[i * 2],
                        y: bestContour.data32S[i * 2 + 1]
                    });
                }
                bestContour.delete();
                return this.orderCorners(corners);
            }
            
            return null;
            
        } catch (error) {
            console.error('Contour detection error:', error);
            return null;
        }
    }
    
    orderCorners(corners) {
        // Order corners as: top-left, top-right, bottom-right, bottom-left
        const center = corners.reduce((acc, corner) => ({
            x: acc.x + corner.x / corners.length,
            y: acc.y + corner.y / corners.length
        }), { x: 0, y: 0 });
        
        return corners.sort((a, b) => {
            const angleA = Math.atan2(a.y - center.y, a.x - center.x);
            const angleB = Math.atan2(b.y - center.y, b.x - center.x);
            return angleA - angleB;
        });
    }
    
    computeHomography(corners, targetSize) {
        if (!this.cv || corners.length !== 4) {
            return this.getFallbackHomography();
        }
        
        try {
            // Source points (detected corners)
            const srcPoints = this.cv.matFromArray(4, 1, this.cv.CV_32FC2, [
                corners[0].x, corners[0].y,
                corners[1].x, corners[1].y,
                corners[2].x, corners[2].y,
                corners[3].x, corners[3].y
            ]);
            
            // Destination points (canonical square)
            const dstPoints = this.cv.matFromArray(4, 1, this.cv.CV_32FC2, [
                0, 0,
                targetSize, 0,
                targetSize, targetSize,
                0, targetSize
            ]);
            
            // Compute homography
            const homography = this.cv.getPerspectiveTransform(srcPoints, dstPoints);
            
            // Extract matrix values
            const matrixData = [];
            for (let i = 0; i < 9; i++) {
                matrixData.push(homography.data64F[i]);
            }
            
            // Clean up
            srcPoints.delete();
            dstPoints.delete();
            homography.delete();
            
            return matrixData;
            
        } catch (error) {
            console.error('Homography computation error:', error);
            return this.getFallbackHomography();
        }
    }
    
    getFallbackHomography() {
        // Return identity matrix as fallback
        return [
            1, 0, 0,
            0, 1, 0,
            0, 0, 1
        ];
    }
}
