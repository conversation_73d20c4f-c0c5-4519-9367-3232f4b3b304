import { ONNXChessDetector } from './onnx-detector.js';
import { BoardDetector } from './board-detector.js';

// Main application logic
class ChessBoardRecognizer {
    constructor() {
        this.video = document.getElementById('video');
        this.canvas = document.getElementById('frameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.status = document.getElementById('status');
        this.fenOutput = document.getElementById('fenOutput');

        this.stream = null;
        this.detector = new ONNXChessDetector();
        this.boardDetector = new BoardDetector();
        this.wasmModule = null;

        this.initializeEventListeners();
    }
    
    initializeEventListeners() {
        document.getElementById('startCameraBtn').addEventListener('click', () => this.startCamera());
        document.getElementById('captureBtn').addEventListener('click', () => this.capturePhoto());
        document.getElementById('fileInput').addEventListener('change', (e) => this.handleFileUpload(e));
        document.getElementById('analyzeBtn').addEventListener('click', () => this.analyzeBoard());
        document.getElementById('copyBtn').addEventListener('click', () => this.copyFenToClipboard());
    }
    
    updateStatus(message, type = 'info') {
        this.status.textContent = message;
        this.status.className = `status ${type}`;
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
    
    async startCamera() {
        try {
            this.updateStatus('Starting camera...', 'loading');
            
            const constraints = {
                video: {
                    facingMode: 'environment', // Use back camera on mobile
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                }
            };
            
            this.stream = await navigator.mediaDevices.getUserMedia(constraints);
            this.video.srcObject = this.stream;
            
            this.video.onloadedmetadata = () => {
                this.video.play();
                document.getElementById('captureBtn').disabled = false;
                this.updateStatus('Camera ready - position chess board in view', 'success');
            };
            
        } catch (error) {
            console.error('Camera error:', error);
            this.updateStatus('Camera access denied or not available', 'error');
        }
    }
    
    capturePhoto() {
        if (!this.video.videoWidth || !this.video.videoHeight) {
            this.updateStatus('Video not ready', 'error');
            return;
        }
        
        // Set canvas size to match video
        this.canvas.width = this.video.videoWidth;
        this.canvas.height = this.video.videoHeight;
        
        // Draw current video frame to canvas
        this.ctx.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);
        
        // Show the captured image
        this.canvas.style.display = 'block';
        this.video.style.display = 'none';
        
        // Enable analyze button
        document.getElementById('analyzeBtn').disabled = false;
        this.updateStatus('Photo captured - ready to analyze', 'success');
    }
    
    async handleFileUpload(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        try {
            this.updateStatus('Loading image...', 'loading');
            
            const img = new Image();
            img.onload = () => {
                // Resize image if too large
                const maxSize = 1024;
                let { width, height } = img;
                
                if (width > maxSize || height > maxSize) {
                    const scale = Math.min(maxSize / width, maxSize / height);
                    width *= scale;
                    height *= scale;
                }
                
                this.canvas.width = width;
                this.canvas.height = height;
                this.ctx.drawImage(img, 0, 0, width, height);
                
                // Show the image
                this.canvas.style.display = 'block';
                this.video.style.display = 'none';
                
                // Enable analyze button
                document.getElementById('analyzeBtn').disabled = false;
                this.updateStatus('Image loaded - ready to analyze', 'success');
            };
            
            img.src = URL.createObjectURL(file);
            
        } catch (error) {
            console.error('File upload error:', error);
            this.updateStatus('Error loading image', 'error');
        }
    }
    
    async loadDependencies() {
        try {
            this.updateStatus('Loading AI models and libraries...', 'loading');

            // Load WASM module
            if (!this.wasmModule) {
                const wasmModule = await import('/pkg/chess_wasm.js');
                await wasmModule.default();
                this.wasmModule = wasmModule;
                console.log('WASM module loaded');
            }

            // Load ONNX detector
            if (!this.detector.modelLoaded) {
                await this.detector.loadModel('/model.onnx');
            }

            // Initialize board detector (includes OpenCV.js)
            await this.boardDetector.initialize();

            return true;

        } catch (error) {
            console.error('Error loading dependencies:', error);
            this.updateStatus('Error loading AI models', 'error');
            return false;
        }
    }
    
    async analyzeBoard() {
        try {
            this.updateStatus('Analyzing chess board...', 'loading');
            
            // Load dependencies if not already loaded
            const dependenciesLoaded = await this.loadDependencies();
            if (!dependenciesLoaded) {
                return;
            }
            
            // Step 1: Detect pieces using ONNX model
            this.updateStatus('Detecting chess pieces...', 'loading');
            const detections = await this.detectPieces();
            
            // Step 2: Detect board corners and compute homography
            this.updateStatus('Detecting board corners...', 'loading');
            const homography = await this.detectBoardCorners();
            
            // Step 3: Map pieces to squares and generate FEN
            this.updateStatus('Generating FEN notation...', 'loading');
            const fen = this.generateFEN(detections, homography);
            
            // Display result
            this.fenOutput.textContent = fen;
            document.getElementById('copyBtn').style.display = 'block';
            this.updateStatus('Analysis complete!', 'success');
            
        } catch (error) {
            console.error('Analysis error:', error);
            this.updateStatus('Error analyzing board', 'error');
        }
    }
    
    async detectPieces() {
        return await this.detector.detectPieces(this.canvas);
    }
    
    async detectBoardCorners() {
        return await this.boardDetector.detectBoardCorners(this.canvas);
    }
    
    generateFEN(detections, homography) {
        if (!this.wasmModule) {
            return 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1'; // Starting position
        }
        
        // Convert detections to flat array format expected by WASM
        const flatDetections = new Float32Array(detections.length * 6);
        detections.forEach((det, i) => {
            const offset = i * 6;
            flatDetections[offset] = det.x;
            flatDetections[offset + 1] = det.y;
            flatDetections[offset + 2] = det.width;
            flatDetections[offset + 3] = det.height;
            flatDetections[offset + 4] = det.class_id;
            flatDetections[offset + 5] = det.confidence;
        });
        
        // Convert homography to Float32Array
        const homographyArray = new Float32Array(homography);
        
        // Call WASM function
        const fen = this.wasmModule.map_detections_to_fen(
            homographyArray,
            flatDetections,
            800.0 // board size
        );
        
        return fen;
    }
    
    async copyFenToClipboard() {
        try {
            await navigator.clipboard.writeText(this.fenOutput.textContent);
            this.updateStatus('FEN copied to clipboard!', 'success');
        } catch (error) {
            console.error('Clipboard error:', error);
            this.updateStatus('Could not copy to clipboard', 'error');
        }
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new ChessBoardRecognizer();
});
