// ONNX-based chess piece detection
export class ONNXChessDetector {
    constructor() {
        this.session = null;
        this.inputName = null;
        this.outputNames = null;
        this.modelLoaded = false;
    }
    
    async loadModel(modelPath = '/model.onnx') {
        try {
            const ort = await import('onnxruntime-web');
            
            // Configure ONNX Runtime for web
            ort.env.wasm.wasmPaths = '/node_modules/onnxruntime-web/dist/';
            ort.env.logLevel = 'warning';
            
            console.log('Loading ONNX model from:', modelPath);
            
            this.session = await ort.InferenceSession.create(modelPath, {
                executionProviders: ['wasm'],
                graphOptimizationLevel: 'all'
            });
            
            // Get input and output names
            this.inputName = this.session.inputNames[0];
            this.outputNames = this.session.outputNames;
            
            console.log('Model loaded successfully');
            console.log('Input name:', this.inputName);
            console.log('Output names:', this.outputNames);
            
            this.modelLoaded = true;
            return true;
            
        } catch (error) {
            console.error('Failed to load ONNX model:', error);
            this.modelLoaded = false;
            return false;
        }
    }
    
    async detectPieces(canvas) {
        if (!this.modelLoaded) {
            console.warn('Model not loaded, using mock detection');
            return this.getMockDetections(canvas);
        }
        
        try {
            // Preprocess image
            const inputTensor = this.preprocessImage(canvas);
            
            // Run inference
            const feeds = { [this.inputName]: inputTensor };
            const results = await this.session.run(feeds);
            
            // Post-process results
            const detections = this.postprocessResults(results, canvas.width, canvas.height);
            
            console.log(`Detected ${detections.length} pieces`);
            return detections;
            
        } catch (error) {
            console.error('Detection error:', error);
            return this.getMockDetections(canvas);
        }
    }
    
    preprocessImage(canvas) {
        const ort = window.ort || require('onnxruntime-web');

        // YOLOv8 standard input size
        const modelWidth = 640;
        const modelHeight = 640;

        // Create a temporary canvas for resizing with proper aspect ratio handling
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = modelWidth;
        tempCanvas.height = modelHeight;
        const tempCtx = tempCanvas.getContext('2d');

        // Fill with gray background (letterboxing)
        tempCtx.fillStyle = '#808080';
        tempCtx.fillRect(0, 0, modelWidth, modelHeight);

        // Calculate scaling to maintain aspect ratio
        const scaleX = modelWidth / canvas.width;
        const scaleY = modelHeight / canvas.height;
        const scale = Math.min(scaleX, scaleY);

        const scaledWidth = canvas.width * scale;
        const scaledHeight = canvas.height * scale;
        const offsetX = (modelWidth - scaledWidth) / 2;
        const offsetY = (modelHeight - scaledHeight) / 2;

        // Draw image centered with letterboxing
        tempCtx.drawImage(canvas, offsetX, offsetY, scaledWidth, scaledHeight);

        // Get image data
        const imageData = tempCtx.getImageData(0, 0, modelWidth, modelHeight);
        const { data } = imageData;

        // Convert to RGB and normalize (0-1) in CHW format for YOLOv8
        const inputArray = new Float32Array(3 * modelWidth * modelHeight);

        for (let i = 0; i < modelHeight; i++) {
            for (let j = 0; j < modelWidth; j++) {
                const pixelIndex = (i * modelWidth + j) * 4;
                const baseIndex = i * modelWidth + j;

                // YOLOv8 expects RGB in CHW format (Channel-Height-Width)
                // Normalize to 0-1 range
                inputArray[baseIndex] = data[pixelIndex] / 255.0;     // R channel
                inputArray[baseIndex + modelWidth * modelHeight] = data[pixelIndex + 1] / 255.0; // G channel
                inputArray[baseIndex + 2 * modelWidth * modelHeight] = data[pixelIndex + 2] / 255.0; // B channel
            }
        }

        // Store scaling info for post-processing
        this.preprocessInfo = {
            scale,
            offsetX,
            offsetY,
            originalWidth: canvas.width,
            originalHeight: canvas.height
        };

        // Create tensor with shape [1, 3, height, width] (NCHW format)
        return new ort.Tensor('float32', inputArray, [1, 3, modelHeight, modelWidth]);
    }
    
    postprocessResults(results, originalWidth, originalHeight) {
        const output = results[this.outputNames[0]];
        const outputData = output.data;
        const outputShape = output.dims;

        console.log('YOLOv8 Output shape:', outputShape);

        const detections = [];
        const confidenceThreshold = 0.25; // Lower threshold for better recall
        const numClasses = 12; // 6 white + 6 black pieces

        // YOLOv8 output format: [1, 4+classes, 8400] = [1, 16, 8400] for chess
        // First 4 are bbox (cx, cy, w, h), then 12 class scores
        if (outputShape.length === 3) {
            const batchSize = outputShape[0];
            const numFeatures = outputShape[1]; // 4 + numClasses
            const numDetections = outputShape[2]; // 8400

            for (let i = 0; i < numDetections; i++) {
                // Extract bbox coordinates (center format)
                const cx = outputData[i];
                const cy = outputData[numDetections + i];
                const w = outputData[2 * numDetections + i];
                const h = outputData[3 * numDetections + i];

                // Extract class scores and find max
                let maxScore = 0;
                let maxClass = -1;

                for (let c = 0; c < numClasses; c++) {
                    const scoreIndex = (4 + c) * numDetections + i;
                    const score = outputData[scoreIndex];
                    if (score > maxScore) {
                        maxScore = score;
                        maxClass = c;
                    }
                }

                if (maxScore > confidenceThreshold) {
                    // Convert from model coordinates back to original image coordinates
                    // Account for letterboxing applied during preprocessing
                    const { scale, offsetX, offsetY } = this.preprocessInfo || {
                        scale: 1, offsetX: 0, offsetY: 0
                    };

                    // Convert from model space to letterboxed space
                    const modelX = cx - w / 2;
                    const modelY = cy - h / 2;

                    // Remove letterbox offset and scale back
                    const origX = (modelX - offsetX) / scale;
                    const origY = (modelY - offsetY) / scale;
                    const origW = w / scale;
                    const origH = h / scale;

                    // Clamp to image bounds
                    const clampedX = Math.max(0, Math.min(origX, originalWidth - origW));
                    const clampedY = Math.max(0, Math.min(origY, originalHeight - origH));
                    const clampedW = Math.min(origW, originalWidth - clampedX);
                    const clampedH = Math.min(origH, originalHeight - clampedY);

                    if (clampedW > 0 && clampedH > 0) {
                        detections.push({
                            x: clampedX,
                            y: clampedY,
                            width: clampedW,
                            height: clampedH,
                            class_id: maxClass,
                            confidence: maxScore
                        });
                    }
                }
            }
        }

        console.log(`Raw detections: ${detections.length}`);

        // Apply Non-Maximum Suppression
        const finalDetections = this.applyNMS(detections, 0.4);
        console.log(`Final detections after NMS: ${finalDetections.length}`);

        return finalDetections;
    }
    
    applyNMS(detections, iouThreshold) {
        // Sort by confidence
        detections.sort((a, b) => b.confidence - a.confidence);
        
        const keep = [];
        const suppressed = new Set();
        
        for (let i = 0; i < detections.length; i++) {
            if (suppressed.has(i)) continue;
            
            keep.push(detections[i]);
            
            for (let j = i + 1; j < detections.length; j++) {
                if (suppressed.has(j)) continue;
                
                const iou = this.calculateIoU(detections[i], detections[j]);
                if (iou > iouThreshold) {
                    suppressed.add(j);
                }
            }
        }
        
        return keep;
    }
    
    calculateIoU(box1, box2) {
        const x1 = Math.max(box1.x, box2.x);
        const y1 = Math.max(box1.y, box2.y);
        const x2 = Math.min(box1.x + box1.width, box2.x + box2.width);
        const y2 = Math.min(box1.y + box1.height, box2.y + box2.height);
        
        if (x2 <= x1 || y2 <= y1) return 0;
        
        const intersection = (x2 - x1) * (y2 - y1);
        const area1 = box1.width * box1.height;
        const area2 = box2.width * box2.height;
        const union = area1 + area2 - intersection;
        
        return intersection / union;
    }
    
    getMockDetections(canvas) {
        // Generate more realistic mock detections based on chess board analysis
        const mockDetections = [];

        // Try to detect a chess board pattern in the image
        const boardRegions = this.detectChessBoardRegions(canvas);

        if (boardRegions.length > 0) {
            // Use detected board regions
            const board = boardRegions[0]; // Use the first/best detected board
            const squareWidth = board.width / 8;
            const squareHeight = board.height / 8;

            // Generate pieces for a realistic starting position
            const startingPosition = [
                // Black pieces (rank 8)
                { file: 0, rank: 0, piece: 9 },  // Black Rook
                { file: 1, rank: 0, piece: 7 },  // Black Knight
                { file: 2, rank: 0, piece: 8 },  // Black Bishop
                { file: 3, rank: 0, piece: 10 }, // Black Queen
                { file: 4, rank: 0, piece: 11 }, // Black King
                { file: 5, rank: 0, piece: 8 },  // Black Bishop
                { file: 6, rank: 0, piece: 7 },  // Black Knight
                { file: 7, rank: 0, piece: 9 },  // Black Rook

                // Black pawns (rank 7)
                ...Array.from({length: 8}, (_, i) => ({ file: i, rank: 1, piece: 6 })),

                // White pawns (rank 2)
                ...Array.from({length: 8}, (_, i) => ({ file: i, rank: 6, piece: 0 })),

                // White pieces (rank 1)
                { file: 0, rank: 7, piece: 3 },  // White Rook
                { file: 1, rank: 7, piece: 1 },  // White Knight
                { file: 2, rank: 7, piece: 2 },  // White Bishop
                { file: 3, rank: 7, piece: 4 },  // White Queen
                { file: 4, rank: 7, piece: 5 },  // White King
                { file: 5, rank: 7, piece: 2 },  // White Bishop
                { file: 6, rank: 7, piece: 1 },  // White Knight
                { file: 7, rank: 7, piece: 3 },  // White Rook
            ];

            startingPosition.forEach(({ file, rank, piece }) => {
                const x = board.x + file * squareWidth + squareWidth * 0.1;
                const y = board.y + rank * squareHeight + squareHeight * 0.1;
                const width = squareWidth * 0.8;
                const height = squareHeight * 0.8;

                mockDetections.push({
                    x,
                    y,
                    width,
                    height,
                    class_id: piece,
                    confidence: 0.75 + Math.random() * 0.2
                });
            });
        } else {
            // Fallback to simple grid-based detection
            const pieceSize = Math.min(canvas.width, canvas.height) / 12;
            const pieces = [
                { x: canvas.width * 0.1, y: canvas.height * 0.1, class_id: 3 },
                { x: canvas.width * 0.2, y: canvas.height * 0.1, class_id: 1 },
                { x: canvas.width * 0.3, y: canvas.height * 0.1, class_id: 2 },
                { x: canvas.width * 0.4, y: canvas.height * 0.1, class_id: 4 },
                { x: canvas.width * 0.5, y: canvas.height * 0.1, class_id: 5 },
            ];

            pieces.forEach(piece => {
                mockDetections.push({
                    x: piece.x - pieceSize / 2,
                    y: piece.y - pieceSize / 2,
                    width: pieceSize,
                    height: pieceSize,
                    class_id: piece.class_id,
                    confidence: 0.8 + Math.random() * 0.2
                });
            });
        }

        console.log('Generated mock detections:', mockDetections.length);
        return mockDetections;
    }

    detectChessBoardRegions(canvas) {
        // Simple chess board detection based on color patterns
        const ctx = canvas.getContext('2d');
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        // Look for alternating light/dark patterns that might indicate a chess board
        const regions = [];

        // Simple heuristic: look for rectangular regions with alternating colors
        const minSize = Math.min(canvas.width, canvas.height) * 0.3;

        // For now, assume the whole canvas is a chess board if it's roughly square
        if (Math.abs(canvas.width - canvas.height) < Math.min(canvas.width, canvas.height) * 0.2) {
            regions.push({
                x: canvas.width * 0.05,
                y: canvas.height * 0.05,
                width: canvas.width * 0.9,
                height: canvas.height * 0.9
            });
        }

        return regions;
    }
}
