// ONNX-based chess piece detection
export class ONNXChessDetector {
    constructor() {
        this.session = null;
        this.inputName = null;
        this.outputNames = null;
        this.modelLoaded = false;
    }
    
    async loadModel(modelPath = '/model.onnx') {
        try {
            const ort = await import('onnxruntime-web');
            
            // Configure ONNX Runtime for web
            ort.env.wasm.wasmPaths = '/node_modules/onnxruntime-web/dist/';
            ort.env.logLevel = 'warning';
            
            console.log('Loading ONNX model from:', modelPath);
            
            this.session = await ort.InferenceSession.create(modelPath, {
                executionProviders: ['wasm'],
                graphOptimizationLevel: 'all'
            });
            
            // Get input and output names
            this.inputName = this.session.inputNames[0];
            this.outputNames = this.session.outputNames;
            
            console.log('Model loaded successfully');
            console.log('Input name:', this.inputName);
            console.log('Output names:', this.outputNames);
            
            this.modelLoaded = true;
            return true;
            
        } catch (error) {
            console.error('Failed to load ONNX model:', error);
            this.modelLoaded = false;
            return false;
        }
    }
    
    async detectPieces(canvas) {
        if (!this.modelLoaded) {
            console.warn('Model not loaded, using mock detection');
            return this.getMockDetections(canvas);
        }
        
        try {
            // Preprocess image
            const inputTensor = this.preprocessImage(canvas);
            
            // Run inference
            const feeds = { [this.inputName]: inputTensor };
            const results = await this.session.run(feeds);
            
            // Post-process results
            const detections = this.postprocessResults(results, canvas.width, canvas.height);
            
            console.log(`Detected ${detections.length} pieces`);
            return detections;
            
        } catch (error) {
            console.error('Detection error:', error);
            return this.getMockDetections(canvas);
        }
    }
    
    preprocessImage(canvas) {
        const ort = window.ort || require('onnxruntime-web');
        
        // Standard YOLO input size
        const modelWidth = 640;
        const modelHeight = 640;
        
        // Create a temporary canvas for resizing
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = modelWidth;
        tempCanvas.height = modelHeight;
        const tempCtx = tempCanvas.getContext('2d');
        
        // Draw and resize image
        tempCtx.drawImage(canvas, 0, 0, modelWidth, modelHeight);
        
        // Get image data
        const imageData = tempCtx.getImageData(0, 0, modelWidth, modelHeight);
        const { data } = imageData;
        
        // Convert to RGB and normalize (0-1)
        const inputArray = new Float32Array(3 * modelWidth * modelHeight);
        
        for (let i = 0; i < modelHeight; i++) {
            for (let j = 0; j < modelWidth; j++) {
                const pixelIndex = (i * modelWidth + j) * 4;
                const tensorIndex = i * modelWidth + j;
                
                // RGB channels (skip alpha)
                inputArray[tensorIndex] = data[pixelIndex] / 255.0;     // R
                inputArray[tensorIndex + modelWidth * modelHeight] = data[pixelIndex + 1] / 255.0; // G
                inputArray[tensorIndex + 2 * modelWidth * modelHeight] = data[pixelIndex + 2] / 255.0; // B
            }
        }
        
        // Create tensor with shape [1, 3, height, width]
        return new ort.Tensor('float32', inputArray, [1, 3, modelHeight, modelWidth]);
    }
    
    postprocessResults(results, originalWidth, originalHeight) {
        // This depends on the specific model output format
        // For YOLOv8, typically outputs are [1, 84, 8400] where:
        // - 84 = 4 (bbox) + 80 (classes) for COCO, or 4 + 12 for chess pieces
        // - 8400 = number of anchor points
        
        const output = results[this.outputNames[0]];
        const outputData = output.data;
        const outputShape = output.dims;
        
        console.log('Output shape:', outputShape);
        
        const detections = [];
        const confidenceThreshold = 0.5;
        const numClasses = 12; // 6 white + 6 black pieces
        
        // Parse output based on shape
        if (outputShape.length === 3 && outputShape[2] > outputShape[1]) {
            // Format: [1, features, detections] - transpose needed
            const numDetections = outputShape[2];
            const numFeatures = outputShape[1];
            
            for (let i = 0; i < numDetections; i++) {
                // Extract bbox coordinates (first 4 features)
                const x = outputData[i];
                const y = outputData[numDetections + i];
                const w = outputData[2 * numDetections + i];
                const h = outputData[3 * numDetections + i];
                
                // Extract class scores (remaining features)
                let maxScore = 0;
                let maxClass = -1;
                
                for (let c = 0; c < numClasses; c++) {
                    const score = outputData[(4 + c) * numDetections + i];
                    if (score > maxScore) {
                        maxScore = score;
                        maxClass = c;
                    }
                }
                
                if (maxScore > confidenceThreshold) {
                    // Convert from model coordinates to original image coordinates
                    const scaleX = originalWidth / 640;
                    const scaleY = originalHeight / 640;
                    
                    detections.push({
                        x: (x - w / 2) * scaleX,
                        y: (y - h / 2) * scaleY,
                        width: w * scaleX,
                        height: h * scaleY,
                        class_id: maxClass,
                        confidence: maxScore
                    });
                }
            }
        }
        
        // Apply Non-Maximum Suppression
        return this.applyNMS(detections, 0.4);
    }
    
    applyNMS(detections, iouThreshold) {
        // Sort by confidence
        detections.sort((a, b) => b.confidence - a.confidence);
        
        const keep = [];
        const suppressed = new Set();
        
        for (let i = 0; i < detections.length; i++) {
            if (suppressed.has(i)) continue;
            
            keep.push(detections[i]);
            
            for (let j = i + 1; j < detections.length; j++) {
                if (suppressed.has(j)) continue;
                
                const iou = this.calculateIoU(detections[i], detections[j]);
                if (iou > iouThreshold) {
                    suppressed.add(j);
                }
            }
        }
        
        return keep;
    }
    
    calculateIoU(box1, box2) {
        const x1 = Math.max(box1.x, box2.x);
        const y1 = Math.max(box1.y, box2.y);
        const x2 = Math.min(box1.x + box1.width, box2.x + box2.width);
        const y2 = Math.min(box1.y + box1.height, box2.y + box2.height);
        
        if (x2 <= x1 || y2 <= y1) return 0;
        
        const intersection = (x2 - x1) * (y2 - y1);
        const area1 = box1.width * box1.height;
        const area2 = box2.width * box2.height;
        const union = area1 + area2 - intersection;
        
        return intersection / union;
    }
    
    getMockDetections(canvas) {
        // Generate mock detections for testing
        const mockDetections = [];
        const pieceSize = Math.min(canvas.width, canvas.height) / 12;
        
        // Add some sample pieces in different positions
        const pieces = [
            { x: canvas.width * 0.1, y: canvas.height * 0.1, class_id: 3 }, // White Rook
            { x: canvas.width * 0.2, y: canvas.height * 0.1, class_id: 1 }, // White Knight
            { x: canvas.width * 0.3, y: canvas.height * 0.1, class_id: 2 }, // White Bishop
            { x: canvas.width * 0.4, y: canvas.height * 0.1, class_id: 4 }, // White Queen
            { x: canvas.width * 0.5, y: canvas.height * 0.1, class_id: 5 }, // White King
            { x: canvas.width * 0.1, y: canvas.height * 0.2, class_id: 0 }, // White Pawn
            { x: canvas.width * 0.2, y: canvas.height * 0.2, class_id: 0 }, // White Pawn
            
            { x: canvas.width * 0.1, y: canvas.height * 0.8, class_id: 9 }, // Black Rook
            { x: canvas.width * 0.2, y: canvas.height * 0.8, class_id: 7 }, // Black Knight
            { x: canvas.width * 0.3, y: canvas.height * 0.8, class_id: 8 }, // Black Bishop
            { x: canvas.width * 0.4, y: canvas.height * 0.8, class_id: 10 }, // Black Queen
            { x: canvas.width * 0.5, y: canvas.height * 0.8, class_id: 11 }, // Black King
            { x: canvas.width * 0.1, y: canvas.height * 0.7, class_id: 6 }, // Black Pawn
            { x: canvas.width * 0.2, y: canvas.height * 0.7, class_id: 6 }, // Black Pawn
        ];
        
        pieces.forEach(piece => {
            mockDetections.push({
                x: piece.x - pieceSize / 2,
                y: piece.y - pieceSize / 2,
                width: pieceSize,
                height: pieceSize,
                class_id: piece.class_id,
                confidence: 0.8 + Math.random() * 0.2
            });
        });
        
        console.log('Generated mock detections:', mockDetections.length);
        return mockDetections;
    }
}
