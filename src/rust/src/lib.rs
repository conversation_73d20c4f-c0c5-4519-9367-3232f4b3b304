use wasm_bindgen::prelude::*;

// Import the `console.log` function from the browser
#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = console)]
    fn log(s: &str);
}

// Define a macro for easier console logging
macro_rules! console_log {
    ($($t:tt)*) => (log(&format_args!($($t)*).to_string()))
}

#[wasm_bindgen]
pub struct Detection {
    pub x: f32,
    pub y: f32,
    pub width: f32,
    pub height: f32,
    pub class_id: i32,
    pub confidence: f32,
}

#[wasm_bindgen]
impl Detection {
    #[wasm_bindgen(constructor)]
    pub fn new(x: f32, y: f32, width: f32, height: f32, class_id: i32, confidence: f32) -> Detection {
        Detection { x, y, width, height, class_id, confidence }
    }
    
    #[wasm_bindgen(getter)]
    pub fn center_x(&self) -> f32 {
        self.x + self.width / 2.0
    }
    
    #[wasm_bindgen(getter)]
    pub fn center_y(&self) -> f32 {
        self.y + self.height / 2.0
    }
}

#[wasm_bindgen]
pub fn map_detections_to_fen(
    homography_matrix: &[f32], // 9 elements: 3x3 matrix in row-major order
    detections: &[f32],        // flat array: [x,y,w,h,class_id,confidence] repeated
    board_size: f32,           // canonical board size (e.g., 800.0)
) -> String {
    console_log!("Processing {} detections", detections.len() / 6);
    
    // Initialize empty 8x8 board
    let mut board = [[' '; 8]; 8];
    
    // Process detections in chunks of 6 (x,y,w,h,class_id,confidence)
    for chunk in detections.chunks(6) {
        if chunk.len() != 6 {
            continue;
        }
        
        let x = chunk[0];
        let y = chunk[1];
        let w = chunk[2];
        let h = chunk[3];
        let class_id = chunk[4] as i32;
        let confidence = chunk[5];
        
        // Skip low confidence detections
        if confidence < 0.5 {
            continue;
        }
        
        // Calculate piece center
        let center_x = x + w / 2.0;
        let center_y = y + h / 2.0;
        
        // Apply homography transformation to map to board coordinates
        let board_coords = apply_homography(homography_matrix, center_x, center_y);
        
        // Check if coordinates are within board bounds
        if board_coords.0 < 0.0 || board_coords.1 < 0.0 || 
           board_coords.0 >= board_size || board_coords.1 >= board_size {
            continue;
        }
        
        // Convert to board square indices (0-7)
        let file = (board_coords.0 / (board_size / 8.0)).floor() as usize;
        let rank = 7 - (board_coords.1 / (board_size / 8.0)).floor() as usize; // Flip Y axis
        
        if file < 8 && rank < 8 {
            let piece_symbol = class_id_to_piece_symbol(class_id);
            if piece_symbol != ' ' {
                board[rank][file] = piece_symbol;
                console_log!("Placed {} at {}{}",
                    piece_symbol,
                    (b'a' + file as u8) as char,
                    rank + 1
                );
            }
        }
    }
    
    // Convert board to FEN notation
    board_to_fen(&board)
}

fn apply_homography(matrix: &[f32], x: f32, y: f32) -> (f32, f32) {
    if matrix.len() != 9 {
        return (x, y); // Return original coordinates if matrix is invalid
    }
    
    // Apply homography transformation: [x', y', w'] = H * [x, y, 1]
    let w = matrix[6] * x + matrix[7] * y + matrix[8];
    
    if w.abs() < 1e-8 {
        return (x, y); // Avoid division by zero
    }
    
    let transformed_x = (matrix[0] * x + matrix[1] * y + matrix[2]) / w;
    let transformed_y = (matrix[3] * x + matrix[4] * y + matrix[5]) / w;
    
    (transformed_x, transformed_y)
}

fn class_id_to_piece_symbol(class_id: i32) -> char {
    // Map class IDs to chess piece symbols
    // Assuming standard order: white pieces (0-5), black pieces (6-11)
    match class_id {
        0 => 'P', // White Pawn
        1 => 'N', // White Knight
        2 => 'B', // White Bishop
        3 => 'R', // White Rook
        4 => 'Q', // White Queen
        5 => 'K', // White King
        6 => 'p', // Black Pawn
        7 => 'n', // Black Knight
        8 => 'b', // Black Bishop
        9 => 'r', // Black Rook
        10 => 'q', // Black Queen
        11 => 'k', // Black King
        _ => ' ',  // Empty or unknown
    }
}

fn board_to_fen(board: &[[char; 8]; 8]) -> String {
    let mut fen_parts = Vec::new();
    
    // Process each rank (row)
    for rank in board.iter() {
        let mut rank_str = String::new();
        let mut empty_count = 0;
        
        for &square in rank.iter() {
            if square == ' ' {
                empty_count += 1;
            } else {
                if empty_count > 0 {
                    rank_str.push_str(&empty_count.to_string());
                    empty_count = 0;
                }
                rank_str.push(square);
            }
        }
        
        // Add remaining empty squares
        if empty_count > 0 {
            rank_str.push_str(&empty_count.to_string());
        }
        
        fen_parts.push(rank_str);
    }
    
    // Join ranks with '/' and add game state info
    // For simplicity, we assume it's white's turn with no castling/en passant
    format!("{} w - - 0 1", fen_parts.join("/"))
}

#[wasm_bindgen]
pub fn validate_fen(fen: &str) -> bool {
    // Basic FEN validation
    let parts: Vec<&str> = fen.split_whitespace().collect();
    
    if parts.len() != 6 {
        return false;
    }
    
    let board_part = parts[0];
    let ranks: Vec<&str> = board_part.split('/').collect();
    
    if ranks.len() != 8 {
        return false;
    }
    
    // Validate each rank
    for rank in ranks {
        let mut file_count = 0;
        for c in rank.chars() {
            if c.is_ascii_digit() {
                let digit = c.to_digit(10).unwrap_or(0) as usize;
                if digit == 0 || digit > 8 {
                    return false;
                }
                file_count += digit;
            } else if "pnbrqkPNBRQK".contains(c) {
                file_count += 1;
            } else {
                return false;
            }
        }
        
        if file_count != 8 {
            return false;
        }
    }
    
    true
}

#[wasm_bindgen(start)]
pub fn main() {
    console_log!("Chess WASM module initialized");
}
