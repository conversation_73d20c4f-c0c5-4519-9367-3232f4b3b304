{"name": "chess-board-recognition", "version": "1.0.0", "description": "Web application for chess board recognition with FEN export", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build-rust": "cd src/rust && cargo build --release --target wasm32-unknown-unknown && wasm-bindgen target/wasm32-unknown-unknown/release/chess_wasm.wasm --out-dir ../../public/pkg --target web", "serve": "vite preview --host 0.0.0.0 --port 3000"}, "dependencies": {"onnxruntime-web": "^1.16.3"}, "devDependencies": {"vite": "^5.0.0"}, "keywords": ["chess", "computer-vision", "onnx", "webassembly", "opencv"], "author": "Chess AI Project", "license": "MIT"}