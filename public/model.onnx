
# Mock ONNX Model for Chess Piece Detection
# This is a placeholder file for development purposes.
# Replace this with a real trained model for production use.

Model Info:
- Input: RGB image (640x640)
- Output: Detections [x, y, width, height, class_id, confidence]
- Classes: 12 chess pieces (6 white + 6 black)
  0: White Pawn, 1: <PERSON>, 2: <PERSON>, 3: <PERSON> Rook, 4: <PERSON> Queen, 5: <PERSON>
  6: Black Pawn, 7: <PERSON> Knight, 8: <PERSON>, 9: Black Rook, 10: <PERSON> Queen, 11: Black King

To get a real model:
1. Train your own using Roboflow: https://universe.roboflow.com/object-detection/chess-full
2. Use a pre-trained model from Ultralytics YOLOv8
3. Convert existing PyTorch/TensorFlow models to ONNX format

For development, the application will use mock detections when this file is present.
