<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>Chess Board Recognition</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            margin-bottom: 30px;
            color: #4CAF50;
        }
        
        .camera-section {
            margin-bottom: 30px;
        }
        
        #video {
            width: 100%;
            max-width: 640px;
            height: auto;
            border-radius: 10px;
            background: #333;
        }
        
        #frameCanvas {
            width: 100%;
            max-width: 640px;
            height: auto;
            border-radius: 10px;
            border: 2px solid #4CAF50;
            margin: 20px 0;
        }
        
        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            margin: 20px 0;
        }
        
        button, input[type="file"] {
            padding: 15px 25px;
            font-size: 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        button {
            background: #4CAF50;
            color: white;
        }
        
        button:hover:not(:disabled) {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        
        input[type="file"] {
            background: #2196F3;
            color: white;
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: #333;
            min-height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .loading {
            color: #FFC107;
        }
        
        .error {
            color: #f44336;
        }
        
        .success {
            color: #4CAF50;
        }
        
        #fenOutput {
            background: #2a2a2a;
            border: 1px solid #555;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            word-break: break-all;
            white-space: pre-wrap;
            min-height: 100px;
        }
        
        .copy-btn {
            background: #FF9800;
            margin-top: 10px;
        }
        
        @media (max-width: 600px) {
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            button, input[type="file"] {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Chess Board Recognition</h1>
        
        <div class="camera-section">
            <video id="video" playsinline autoplay muted></video>
            <canvas id="frameCanvas" width="640" height="640" style="display: none;"></canvas>
        </div>
        
        <div class="controls">
            <button id="startCameraBtn">Start Camera</button>
            <button id="captureBtn" disabled>Capture Photo</button>
            <input type="file" accept="image/*" capture="environment" id="fileInput">
            <button id="analyzeBtn" disabled>Analyze Board</button>
        </div>
        
        <div id="status" class="status">Ready to capture chess board</div>
        
        <div id="fenOutput">FEN notation will appear here...</div>
        
        <button id="copyBtn" class="copy-btn" style="display: none;">Copy FEN to Clipboard</button>
    </div>

    <!-- OpenCV.js - Load asynchronously -->
    <script async src="https://docs.opencv.org/4.8.0/opencv.js" onload="console.log('OpenCV.js loaded')"></script>
    <script type="module" src="/src/main.js"></script>
</body>
</html>
