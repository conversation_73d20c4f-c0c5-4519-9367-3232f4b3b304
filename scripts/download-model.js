#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import https from 'https';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Model download configuration
const MODEL_CONFIG = {
    // Real chess piece detection models
    roboflow_chess: {
        url: 'https://github.com/roboflow/chess-piece-detection/releases/download/v1.0/chess-pieces-yolov8.onnx',
        filename: 'chess-pieces-yolov8.onnx',
        description: 'YOLOv8 chess pieces detection model from Roboflow'
    },

    // Alternative: Try to get from Hugging Face
    huggingface_chess: {
        url: 'https://huggingface.co/keremberke/yolov8n-chess-piece-detection/resolve/main/best.onnx',
        filename: 'yolov8n-chess-hf.onnx',
        description: 'YOLOv8 chess piece detection from Hugging Face'
    },

    // Fallback: General YOLOv8 model (not chess-specific)
    yolov8_general: {
        url: 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8n.onnx',
        filename: 'yolov8n-general.onnx',
        description: 'General YOLOv8 nano model (not chess-specific)'
    }
};

async function downloadFile(url, filepath) {
    return new Promise((resolve, reject) => {
        console.log(`Downloading from: ${url}`);
        console.log(`Saving to: ${filepath}`);
        
        const file = fs.createWriteStream(filepath);
        
        https.get(url, (response) => {
            if (response.statusCode === 302 || response.statusCode === 301) {
                // Handle redirect
                return downloadFile(response.headers.location, filepath)
                    .then(resolve)
                    .catch(reject);
            }
            
            if (response.statusCode !== 200) {
                reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
                return;
            }
            
            const totalSize = parseInt(response.headers['content-length'], 10);
            let downloadedSize = 0;
            
            response.on('data', (chunk) => {
                downloadedSize += chunk.length;
                if (totalSize) {
                    const percent = ((downloadedSize / totalSize) * 100).toFixed(1);
                    process.stdout.write(`\rProgress: ${percent}% (${downloadedSize}/${totalSize} bytes)`);
                }
            });
            
            response.pipe(file);
            
            file.on('finish', () => {
                file.close();
                console.log('\nDownload completed!');
                resolve();
            });
            
            file.on('error', (err) => {
                fs.unlink(filepath, () => {}); // Delete partial file
                reject(err);
            });
            
        }).on('error', (err) => {
            reject(err);
        });
    });
}

async function createMockModel(filepath) {
    console.log('Creating mock ONNX model for development...');
    
    // Create a minimal ONNX model structure (this is just a placeholder)
    const mockModelContent = `
# Mock ONNX Model for Chess Piece Detection
# This is a placeholder file for development purposes.
# Replace this with a real trained model for production use.

Model Info:
- Input: RGB image (640x640)
- Output: Detections [x, y, width, height, class_id, confidence]
- Classes: 12 chess pieces (6 white + 6 black)
  0: White Pawn, 1: White Knight, 2: White Bishop, 3: White Rook, 4: White Queen, 5: White King
  6: Black Pawn, 7: Black Knight, 8: Black Bishop, 9: Black Rook, 10: Black Queen, 11: Black King

To get a real model:
1. Train your own using Roboflow: https://universe.roboflow.com/object-detection/chess-full
2. Use a pre-trained model from Ultralytics YOLOv8
3. Convert existing PyTorch/TensorFlow models to ONNX format

For development, the application will use mock detections when this file is present.
`;
    
    fs.writeFileSync(filepath, mockModelContent);
    console.log('Mock model file created.');
}

async function main() {
    const publicDir = path.join(__dirname, '..', 'public');
    const modelPath = path.join(publicDir, 'model.onnx');

    // Ensure public directory exists
    if (!fs.existsSync(publicDir)) {
        fs.mkdirSync(publicDir, { recursive: true });
    }

    console.log('Chess Piece Detection Model Downloader');
    console.log('=====================================');

    // Check if model already exists
    if (fs.existsSync(modelPath)) {
        console.log('Model file already exists at:', modelPath);
        console.log('Delete the file if you want to re-download.');
        return;
    }

    // Try to download a real chess model
    console.log('Attempting to download a real chess piece detection model...');

    for (const [name, config] of Object.entries(MODEL_CONFIG)) {
        console.log(`\nTrying ${config.description}...`);
        try {
            await downloadFile(config.url, modelPath);
            console.log(`Successfully downloaded ${name} model!`);
            console.log('Model is ready for use.');
            return;
        } catch (error) {
            console.log(`Failed to download ${name}: ${error.message}`);
            // Try next model
        }
    }

    // If all downloads failed, create mock model
    console.log('\nAll model downloads failed. Creating mock model for development.');
    console.log('For production, you will need to:');
    console.log('1. Train a model using a chess piece dataset');
    console.log('2. Export it to ONNX format');
    console.log('3. Replace the mock model file');
    console.log('');

    await createMockModel(modelPath);

    console.log('');
    console.log('Setup complete! The application will use mock detections for development.');
    console.log('');
    console.log('Recommended resources:');
    console.log('1. Roboflow Chess Dataset: https://universe.roboflow.com/object-detection/chess-full');
    console.log('2. Hugging Face Models: https://huggingface.co/models?search=chess+yolo');
    console.log('3. Train your own: https://docs.ultralytics.com/modes/train/');
}

// Handle command line execution
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}

export { downloadFile, createMockModel };
